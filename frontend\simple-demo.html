<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KaiNote - Meeting Management Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .section h2 {
            color: #495057;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
        }
        button:hover {
            background: #0056b3;
        }
        .response {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status {
            text-align: center;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .status.online {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.offline {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 KaiNote - Meeting Management System</h1>
        
        <div id="status" class="status">
            Checking backend connection...
        </div>

        <div class="section">
            <h2>📝 Create Meeting</h2>
            <div class="form-group">
                <label for="meetingTitle">Meeting Title:</label>
                <input type="text" id="meetingTitle" placeholder="Weekly Team Standup">
            </div>
            <div class="form-group">
                <label for="meetingTranscript">Meeting Transcript:</label>
                <textarea id="meetingTranscript" placeholder="Paste your meeting transcript here..."></textarea>
            </div>
            <div class="form-group">
                <label for="projectId">Project ID:</label>
                <input type="text" id="projectId" placeholder="project-123">
            </div>
            <button onclick="createMeeting()">Create Meeting</button>
            <div id="createResponse" class="response" style="display:none;"></div>
        </div>

        <div class="section">
            <h2>🤖 AI Process Transcript</h2>
            <div class="form-group">
                <label for="aiTranscript">Transcript to Process:</label>
                <textarea id="aiTranscript" placeholder="Enter meeting transcript for AI analysis..."></textarea>
            </div>
            <button onclick="processTranscript()">Process with AI</button>
            <div id="aiResponse" class="response" style="display:none;"></div>
        </div>

        <div class="section">
            <h2>📅 Schedule Meeting</h2>
            <div class="form-group">
                <label for="scheduleTitle">Meeting Title:</label>
                <input type="text" id="scheduleTitle" placeholder="Client Review Meeting">
            </div>
            <div class="form-group">
                <label for="scheduleDate">Date & Time:</label>
                <input type="datetime-local" id="scheduleDate">
            </div>
            <div class="form-group">
                <label for="scheduleProject">Project ID:</label>
                <input type="text" id="scheduleProject" placeholder="project-456">
            </div>
            <button onclick="scheduleMeeting()">Schedule Meeting</button>
            <div id="scheduleResponse" class="response" style="display:none;"></div>
        </div>

        <div class="section">
            <h2>📊 API Documentation</h2>
            <p>For full API documentation and testing, visit:</p>
            <a href="http://localhost:8000/docs" target="_blank" style="color: #007bff; text-decoration: none; font-weight: 600;">
                🔗 http://localhost:8000/docs
            </a>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';

        // Check backend status
        async function checkBackendStatus() {
            try {
                const response = await fetch(`${API_BASE}/`);
                if (response.ok) {
                    document.getElementById('status').innerHTML = '✅ Backend is running successfully!';
                    document.getElementById('status').className = 'status online';
                } else {
                    throw new Error('Backend not responding');
                }
            } catch (error) {
                document.getElementById('status').innerHTML = '❌ Backend is not running. Please start the backend server.';
                document.getElementById('status').className = 'status offline';
            }
        }

        // Create meeting
        async function createMeeting() {
            const title = document.getElementById('meetingTitle').value;
            const transcript = document.getElementById('meetingTranscript').value;
            const projectId = document.getElementById('projectId').value;

            if (!title || !transcript) {
                alert('Please fill in title and transcript');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/routes/meetings`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: title,
                        transcript: transcript,
                        project_id: projectId || 'default-project'
                    })
                });

                const result = await response.json();
                const responseDiv = document.getElementById('createResponse');
                responseDiv.style.display = 'block';
                responseDiv.textContent = JSON.stringify(result, null, 2);
                responseDiv.className = response.ok ? 'response' : 'response error';
            } catch (error) {
                const responseDiv = document.getElementById('createResponse');
                responseDiv.style.display = 'block';
                responseDiv.textContent = 'Error: ' + error.message;
                responseDiv.className = 'response error';
            }
        }

        // Process transcript with AI
        async function processTranscript() {
            const transcript = document.getElementById('aiTranscript').value;

            if (!transcript) {
                alert('Please enter a transcript');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/routes/api/ai/process-transcript`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        transcript: transcript
                    })
                });

                const result = await response.json();
                const responseDiv = document.getElementById('aiResponse');
                responseDiv.style.display = 'block';
                responseDiv.textContent = JSON.stringify(result, null, 2);
                responseDiv.className = response.ok ? 'response' : 'response error';
            } catch (error) {
                const responseDiv = document.getElementById('aiResponse');
                responseDiv.style.display = 'block';
                responseDiv.textContent = 'Error: ' + error.message;
                responseDiv.className = 'response error';
            }
        }

        // Schedule meeting
        async function scheduleMeeting() {
            const title = document.getElementById('scheduleTitle').value;
            const date = document.getElementById('scheduleDate').value;
            const projectId = document.getElementById('scheduleProject').value;

            if (!title || !date) {
                alert('Please fill in title and date');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/routes/meetings/schedule`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: title,
                        scheduled_time: date,
                        project_id: projectId || 'default-project'
                    })
                });

                const result = await response.json();
                const responseDiv = document.getElementById('scheduleResponse');
                responseDiv.style.display = 'block';
                responseDiv.textContent = JSON.stringify(result, null, 2);
                responseDiv.className = response.ok ? 'response' : 'response error';
            } catch (error) {
                const responseDiv = document.getElementById('scheduleResponse');
                responseDiv.style.display = 'block';
                responseDiv.textContent = 'Error: ' + error.message;
                responseDiv.className = 'response error';
            }
        }

        // Check status on page load
        checkBackendStatus();
    </script>
</body>
</html>
