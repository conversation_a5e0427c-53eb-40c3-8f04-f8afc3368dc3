import React from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "lucide-react";

export const MeetingSubmissionForm = () => {
  return (
    <div className="glassmorphic-card p-6">
      <h2 className="text-xl font-semibold mb-4">Join a Meeting</h2>
      <p className="text-gray-500 mb-6">
        Enter the meeting link below and <PERSON><PERSON><PERSON> will automatically join,
        record, and transcribe it for you.
      </p>
      <div className="flex w-full max-w-lg items-center space-x-2">
        <div className="relative flex-grow">
          <Link className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
          <Input
            type="url"
            placeholder="https://meet.google.com/xyz-abc-pqr"
            className="pl-10"
          />
        </div>
        <Button type="submit" className="bg-blue-600 text-white hover:bg-blue-700">
          Request to Join
        </Button>
      </div>
    </div>
  );
};
