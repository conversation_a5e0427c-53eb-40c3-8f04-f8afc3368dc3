/** AISummary */
export interface AISummary {
  /** Summary */
  summary: string;
  /** Key Decisions */
  key_decisions: string[];
  /** Action Items */
  action_items: string[];
}

/** Body_create_meeting_and_process_content */
export interface BodyCreateMeetingAndProcessContent {
  /** Title */
  title: string;
  /** Projectid */
  projectId: string;
  /** Date */
  date: string;
  /** Attendees */
  attendees?: string | null;
  /** Transcript */
  transcript?: string | null;
  /** Audio File */
  audio_file?: File | null;
}

/** Body_process_transcript */
export interface BodyProcessTranscript {
  /**
   * File
   * @format binary
   */
  file: File;
}

/** HTTPValidationError */
export interface HTTPValidationError {
  /** Detail */
  detail?: ValidationError[];
}

/** HealthResponse */
export interface HealthResponse {
  /** Status */
  status: string;
}

/** Meeting */
export interface Meeting {
  /** Id */
  id: string;
  /** Title */
  title: string;
  /** Project Id */
  project_id: string;
  /** Date */
  date: string;
  /** Status */
  status: string;
}

/** MeetingCreationResponse */
export interface MeetingCreationResponse {
  /** Message */
  message: string;
  /** Meetingid */
  meetingId: string;
  /** Title */
  title: string;
  /** Projectid */
  projectId: string;
  /** Transcript */
  transcript?: string | null;
}

/** ScheduleMeetingRequest */
export interface ScheduleMeetingRequest {
  /** Title */
  title: string;
  /** Projectid */
  projectId: string;
  /** Date */
  date: string;
  /** Time */
  time: string;
  /** Attendees */
  attendees?: string | null;
  /** Notes */
  notes?: string | null;
}

/** ValidationError */
export interface ValidationError {
  /** Location */
  loc: (string | number)[];
  /** Message */
  msg: string;
  /** Error Type */
  type: string;
}

export type CheckHealthData = HealthResponse;

export type ProcessTranscriptData = AISummary;

export type ProcessTranscriptError = HTTPValidationError;

export type ScheduleMeetingData = MeetingCreationResponse;

export type ScheduleMeetingError = HTTPValidationError;

export type CreateMeetingAndProcessContentData = MeetingCreationResponse;

export type CreateMeetingAndProcessContentError = HTTPValidationError;

export interface GetProjectMeetingsParams {
  /** Project Id */
  projectId: string;
}

/** Response Get Project Meetings */
export type GetProjectMeetingsData = Meeting[];

export type GetProjectMeetingsError = HTTPValidationError;
