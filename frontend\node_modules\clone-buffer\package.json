{"name": "clone-buffer", "version": "1.0.0", "description": "Easier Buffer cloning in node.", "author": "Gulp Team <<EMAIL>> (http://gulpjs.com/)", "contributors": ["<PERSON> <<EMAIL>>"], "repository": "gulpjs/clone-buffer", "license": "MIT", "engines": {"node": ">= 0.10"}, "main": "index.js", "files": ["LICENSE", "index.js"], "scripts": {"lint": "eslint . && jscs index.js test/", "pretest": "npm run lint", "test": "mocha --async-only", "cover": "istanbul cover _mocha --report lcovonly", "coveralls": "npm run cover && istanbul-coveralls"}, "dependencies": {}, "devDependencies": {"eslint": "^1.7.3", "eslint-config-gulp": "^2.0.0", "expect": "^1.19.0", "istanbul": "^0.4.3", "istanbul-coveralls": "^1.0.3", "jscs": "^2.3.5", "jscs-preset-gulp": "^1.0.0", "mocha": "^2.4.5"}, "keywords": ["buffer", "clone", "from", "copy"]}