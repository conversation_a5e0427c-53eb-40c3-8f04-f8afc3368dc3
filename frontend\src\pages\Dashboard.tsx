import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useUserGuardContext } from "app/auth";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { CreateProjectModal } from "components/CreateProjectModal";
import { ProjectCard } from "components/ProjectCard";
import { projects, meetings } from "utils/placeholders";
import { MeetingsTable } from "components/MeetingsTable";
import { MeetingSubmissionForm } from "components/MeetingSubmissionForm";
import {
  Plus,
  Upload,
  CheckCircle,
  Clock,
  Calendar,
  ListTodo,
  Briefcase,
  ArrowRight,
  LayoutGrid,
  Folder,
  Share2,
} from "lucide-react";


const Dashboard = () => {
  const navigate = useNavigate();
  const { user } = useUserGuardContext();
  const [isCreateProjectModalOpen, setCreateProjectModalOpen] = useState(false);

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-100">
        <div className="container mx-auto px-4 py-8">
          <header className="flex flex-wrap justify-between items-center mb-8 gap-4">
            <div>
              <h1 className="text-3xl font-bold">
                Welcome back, {user.firstName || "there"}!
              </h1>
              <p className="text-gray-500">
                Here's an overview of your projects and meetings.
              </p>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" className="bg-white/50">
                <Upload className="mr-2 h-4 w-4" />
                Upload
              </Button>
              <Button
                className="bg-blue-600 text-white hover:bg-blue-700"
                onClick={() => setCreateProjectModalOpen(true)}
              >
                <Plus className="mr-2 h-4 w-4" />
                New Project
              </Button>
              <Button variant="outline" onClick={() => navigate("/schedule-meeting")}>Schedule Meeting</Button>
              <Button className="w-full" onClick={() => navigate("/log-meeting")}>
                <Plus className="mr-2 h-4 w-4" />
                Log Past Meeting
              </Button>
            </div>
          </header>

          <Tabs defaultValue="overview">
            <TabsList className="grid w-full grid-cols-5 mb-8">
              <TabsTrigger value="overview">
                <LayoutGrid className="mr-2 h-4 w-4" /> Overview
              </TabsTrigger>
              <TabsTrigger value="projects">
                 <Folder className="mr-2 h-4 w-4" /> Projects
              </TabsTrigger>
              <TabsTrigger value="meetings">
                 <Calendar className="mr-2 h-4 w-4" /> Meetings
              </TabsTrigger>
              <TabsTrigger value="integrations">
                <Share2 className="mr-2 h-4 w-4" /> Integrations
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <main className="lg:col-span-2 space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="glassmorphic-card p-6 flex items-center space-x-4">
                      <div className="p-3 rounded-full bg-blue-100">
                        <Calendar className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-gray-500 text-sm">Meetings This Week</p>
                        <p className="text-2xl font-bold">5</p>
                      </div>
                    </div>
                    <div className="glassmorphic-card p-6 flex items-center space-x-4">
                      <div className="p-3 rounded-full bg-green-100">
                        <CheckCircle className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <p className="text-gray-500 text-sm">Completed Tasks</p>
                        <p className="text-2xl font-bold">12</p>
                      </div>
                    </div>
                     <div className="glassmorphic-card p-6 flex items-center space-x-4">
                      <div className="p-3 rounded-full bg-yellow-100">
                        <Clock className="h-6 w-6 text-yellow-600" />
                      </div>
                      <div>
                        <p className="text-gray-500 text-sm">Pending Tasks</p>
                        <p className="text-2xl font-bold">3</p>
                      </div>
                    </div>
                  </div>

                  <div className="glassmorphic-card p-6">
                    <h2 className="text-xl font-semibold mb-4">Recent Projects</h2>
                    <div className="space-y-4">
                      {projects.slice(0, 2).map((p) => (
                        <div key={p.id} className="flex items-center justify-between p-4 rounded-lg hover:bg-gray-50/50">
                          <div className="flex items-center space-x-4">
                            <Folder className="h-6 w-6 text-blue-500" />
                            <div>
                               <p className="font-semibold">{p.name}</p>
                               <p className="text-sm text-gray-500">{p.client}</p>
                            </div>
                          </div>
                          <Button variant="ghost" size="sm" onClick={() => navigate(`/project-detail?id=${p.id}`)}>
                             View <ArrowRight className="ml-2 h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                </main>
                <aside className="space-y-8">
                  <MeetingSubmissionForm />
                </aside>
              </div>
            </TabsContent>

            <TabsContent value="projects">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {projects.map(p => <ProjectCard key={p.id} project={p} />)}
                </div>
            </TabsContent>

             <TabsContent value="meetings">
               <Tabs defaultValue="upcoming">
                  <TabsList>
                    <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
                    <TabsTrigger value="past">Past</TabsTrigger>
                  </TabsList>
                  <TabsContent value="upcoming">
                      <div className="glassmorphic-card p-6">
                        <MeetingsTable meetings={meetings.filter(m => new Date(m.date) >= new Date())} />
                    </div>
                  </TabsContent>
                  <TabsContent value="past">
                      <div className="glassmorphic-card p-6">
                        <MeetingsTable meetings={meetings.filter(m => new Date(m.date) < new Date())} />
                    </div>
                  </TabsContent>
                </Tabs>
            </TabsContent>
            
            <TabsContent value="integrations">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Google Calendar */}
                <div className="glassmorphic-card p-6 flex flex-col items-start">
                  <div className="flex justify-between items-center w-full mb-4">
                    <img src="/src/assets/google-calendar.png" alt="Google Calendar" className="h-8" />
                    <Button variant="outline" size="sm">Connect</Button>
                  </div>
                  <h3 className="font-semibold text-lg">Google Calendar</h3>
                  <p className="text-gray-500 text-sm mt-1">Automatically import meetings and sync action items.</p>
                </div>

                {/* Outlook */}
                <div className="glassmorphic-card p-6 flex flex-col items-start">
                  <div className="flex justify-between items-center w-full mb-4">
                    <img src="/src/assets/outlook.png" alt="Microsoft Outlook" className="h-8" />
                    <Button variant="outline" size="sm">Connect</Button>
                  </div>
                  <h3 className="font-semibold text-lg">Microsoft Outlook</h3>
                  <p className="text-gray-500 text-sm mt-1">Sync your Outlook calendar to capture every meeting.</p>
                </div>
              </div>
            </TabsContent>


          </Tabs>
        </div>
      </div>
      <CreateProjectModal isOpen={isCreateProjectModalOpen} onClose={() => setCreateProjectModalOpen(false)} />
    </>
  );
};

export default Dashboard;
