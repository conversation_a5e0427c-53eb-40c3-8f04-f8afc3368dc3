import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  ArrowLeft,
  Settings,
  LayoutDashboard,
  Calendar,
  ListTodo,
  FileText,
  Users,
  DollarSign,
  Plus,
} from "lucide-react";
import { useNavigate, useSearchParams } from "react-router-dom";

import { cn } from "@/lib/utils";

import { MeetingCard } from "components/MeetingCard";

import { Meeting } from "types";
import brain from "brain";
import { MeetingsTable } from "components/MeetingsTable";

const ProjectDetail = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const projectId = searchParams.get("id");
  const project = { id: projectId, name: "Project", client: "Client" };
  const [activeView, setActiveView] = useState("dashboard");
  const [projectMeetings, setProjectMeetings] = useState<Meeting[]>([]);

  useEffect(() => {
    if (projectId) {
      const fetchMeetings = async () => {
        try {
          const response = await brain.get_project_meetings({ projectId });
          const data = await response.json();
          setProjectMeetings(data);
        } catch (error) {
          console.error("Failed to fetch meetings:", error);
        }
      };

      fetchMeetings();
    }
  }, [projectId]);


  if (!project) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gradient-to-br from-gray-50 to-blue-100 text-center">
        <h2 className="text-2xl font-bold mb-4">Project Not Found</h2>
        <p className="text-gray-600 mb-8">
          The project you are looking for does not exist.
        </p>
        <Button onClick={() => navigate("/dashboard")}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Dashboard
        </Button>
      </div>
    );
  }
  
  const navItems = [
      { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },
      { id: 'meetings', label: 'Meetings', icon: Calendar },
      { id: 'workflow', label: 'Workflow', icon: ListTodo },
      { id: 'client-hub', label: 'Client Hub', icon: Users },
      { id: 'financials', label: 'Financials', icon: DollarSign },
      { id: 'documentation', label: 'Documentation', icon: FileText },
  ]

  const renderContent = () => {
    switch (activeView) {
      case "dashboard":
        return <div className="glassmorphic-card p-6"><h2 className="text-xl font-semibold">Project Dashboard</h2><p className="text-gray-500 mt-2">Overview of project stats, health, and recent activity.</p></div>;
      case "meetings":
        return (
          <div className="glassmorphic-card p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Meetings</h2>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={() => navigate("/schedule-meeting")}>Schedule</Button>
                <Button size="sm" onClick={() => navigate("/log-meeting")}>
                  <Plus className="mr-2 h-4 w-4" />
                   Log Meeting
                </Button>
              </div>
            </div>
            {projectMeetings.length > 0 ? (
              <MeetingsTable meetings={projectMeetings} />
            ) : (
              <p className="text-gray-500 mt-4 text-center py-8">
                No meetings scheduled for this project yet.
              </p>
            )}
          </div>
        );
      case "workflow":
        return <div className="glassmorphic-card p-6"><h2 className="text-xl font-semibold">Workflow</h2><p className="text-gray-500 mt-2">Dynamic task management and project workflow engine.</p></div>;
      case "client-hub":
        return <div className="glassmorphic-card p-6"><h2 className="text-xl font-semibold">Client Hub</h2><p className="text-gray-500 mt-2">Proactive client updates and communication tools.</p></div>;
      case "financials":
        return <div className="glassmorphic-card p-6"><h2 className="text-xl font-semibold">Financials</h2><p className="text-gray-500 mt-2">Financial management suite with invoicing and cost tracking.</p></div>;
      case "documentation":
        return <div className="glassmorphic-card p-6"><h2 className="text-xl font-semibold">Documentation</h2><p className="text-gray-500 mt-2">Project documents, notes, and deliverables.</p></div>;
      default:
        return null;
    }
  };


  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <header className="flex flex-wrap justify-between items-start mb-8 gap-4">
          <div>
            <Button
              variant="ghost"
              onClick={() => navigate("/dashboard")}
              className="mb-4 -ml-4"
            >
              <ArrowLeft className="mr-2 h-4 w-4" /> Back to Projects
            </Button>
            <h1 className="text-4xl font-bold">{project.name}</h1>
            <p className="text-lg text-gray-500 mt-1">{project.client}</p>
          </div>
          <div className="flex space-x-2 mt-2">
            <Button variant="outline" className="bg-white/50">
              <Settings className="mr-2 h-4 w-4" />
              Project Settings
            </Button>
          </div>
        </header>

        {/* Main Content Area */}
        <div className="flex flex-col lg:flex-row gap-8">
            {/* Sidebar Navigation */}
            <nav className="lg:w-1/4 xl:w-1/5">
                <div className="glassmorphic-card p-4">
                    <ul className="space-y-2">
                        {navItems.map(item => (
                             <li key={item.id}>
                                <Button
                                variant="ghost"
                                className={cn(
                                    "w-full justify-start text-base p-6",
                                    activeView === item.id && "bg-white/50 font-bold"
                                )}
                                onClick={() => setActiveView(item.id)}
                                >
                                <item.icon className="mr-3 h-5 w-5" />
                                {item.label}
                                </Button>
                            </li>
                        ))}
                    </ul>
                </div>
            </nav>

            {/* Content Display */}
            <main className="flex-1">
                {renderContent()}
            </main>
        </div>
      </div>
    </div>
  );
};

export default ProjectDetail;
