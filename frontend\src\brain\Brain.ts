import {
  BodyCreateMeetingAndProcessContent,
  BodyProcessTranscript,
  CheckHealthData,
  CreateMeetingAndProcessContentData,
  CreateMeetingAndProcessContentError,
  GetProjectMeetingsData,
  GetProjectMeetingsError,
  GetProjectMeetingsParams,
  ProcessTranscriptData,
  ProcessTranscriptError,
  ScheduleMeetingData,
  ScheduleMeetingError,
  ScheduleMeetingRequest,
} from "./data-contracts";
import { ContentType, HttpClient, RequestParams } from "./http-client";

export class Brain<SecurityDataType = unknown> extends HttpClient<SecurityDataType> {
  /**
   * @description Check health of application. Returns 200 when OK, 500 when not.
   *
   * @name check_health
   * @summary Check Health
   * @request GET:/_healthz
   */
  check_health = (params: RequestParams = {}) =>
    this.request<CheckHealthData, any>({
      path: `/_healthz`,
      method: "GET",
      ...params,
    });

  /**
   * No description
   *
   * @tags AI, dbtn/module:ai, dbtn/hasAuth
   * @name process_transcript
   * @summary Process Transcript
   * @request POST:/routes/api/ai/process-transcript
   */
  process_transcript = (data: BodyProcessTranscript, params: RequestParams = {}) =>
    this.request<ProcessTranscriptData, ProcessTranscriptError>({
      path: `/routes/api/ai/process-transcript`,
      method: "POST",
      body: data,
      type: ContentType.FormData,
      ...params,
    });

  /**
   * @description Schedules a new meeting for the future. Does not require a transcript or audio file.
   *
   * @tags Meetings, dbtn/module:meetings, dbtn/hasAuth
   * @name schedule_meeting
   * @summary Schedule Meeting
   * @request POST:/routes/meetings/schedule
   */
  schedule_meeting = (data: ScheduleMeetingRequest, params: RequestParams = {}) =>
    this.request<ScheduleMeetingData, ScheduleMeetingError>({
      path: `/routes/meetings/schedule`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      ...params,
    });

  /**
   * @description Creates a new meeting and handles content processing. - If a transcript is provided, it's used directly. - If an audio file is provided, it is transcribed using Whisper. - Either an audio file or a transcript must be provided.
   *
   * @tags Meetings, dbtn/module:meetings, dbtn/hasAuth
   * @name create_meeting_and_process_content
   * @summary Create Meeting And Process Content
   * @request POST:/routes/meetings
   */
  create_meeting_and_process_content = (data: BodyCreateMeetingAndProcessContent, params: RequestParams = {}) =>
    this.request<CreateMeetingAndProcessContentData, CreateMeetingAndProcessContentError>({
      path: `/routes/meetings`,
      method: "POST",
      body: data,
      type: ContentType.FormData,
      ...params,
    });

  /**
   * No description
   *
   * @tags dbtn/module:projects, dbtn/hasAuth
   * @name get_project_meetings
   * @summary Get Project Meetings
   * @request GET:/routes/project-meetings/{project_id}
   */
  get_project_meetings = ({ projectId, ...query }: GetProjectMeetingsParams, params: RequestParams = {}) =>
    this.request<GetProjectMeetingsData, GetProjectMeetingsError>({
      path: `/routes/project-meetings/${projectId}`,
      method: "GET",
      ...params,
    });
}
