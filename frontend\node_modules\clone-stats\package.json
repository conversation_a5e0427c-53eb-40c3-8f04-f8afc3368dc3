{"name": "clone-stats", "description": "Safely clone node's fs.Stats instances without losing their class methods", "version": "1.0.0", "main": "index.js", "browser": "index.js", "dependencies": {}, "devDependencies": {"tape": "~2.3.2"}, "scripts": {"test": "node test"}, "author": "<PERSON> <<EMAIL>> (http://hughsk.io/)", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/hughsk/clone-stats"}, "bugs": {"url": "https://github.com/hughsk/clone-stats/issues"}, "homepage": "https://github.com/hughsk/clone-stats", "keywords": ["stats", "fs", "clone", "copy", "prototype"]}