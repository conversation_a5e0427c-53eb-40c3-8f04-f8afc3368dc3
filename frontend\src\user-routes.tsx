
// THIS FILE IS AUTOGENERATED WHEN PAGES ARE UPDATED
import { lazy } from "react";
import { RouteObject } from "react-router";


import { UserGuard } from "app";


import { StackHandlerRoutes, LoginRedirect } from "app/auth";


const App = lazy(() => import("./pages/App.tsx"));
const Dashboard = lazy(() => import("./pages/Dashboard.tsx"));
const LogMeeting = lazy(() => import("./pages/LogMeeting.tsx"));
const MeetingDetail = lazy(() => import("./pages/MeetingDetail.tsx"));
const ProjectDetail = lazy(() => import("./pages/ProjectDetail.tsx"));
const ScheduleMeeting = lazy(() => import("./pages/ScheduleMeeting.tsx"));

export const userRoutes: RouteObject[] = [

	{ path: "/auth/redirect", element: <LoginRedirect />},
	{ path: "/auth/*", element: <StackHandlerRoutes />},
	{ path: "/", element: <App />},
	{ path: "/dashboard", element: <UserGuard><Dashboard /></UserGuard>},
	{ path: "/log-meeting", element: <UserGuard><LogMeeting /></UserGuard>},
	{ path: "/logmeeting", element: <UserGuard><LogMeeting /></UserGuard>},
	{ path: "/meeting-detail", element: <UserGuard><MeetingDetail /></UserGuard>},
	{ path: "/meetingdetail", element: <UserGuard><MeetingDetail /></UserGuard>},
	{ path: "/project-detail", element: <UserGuard><ProjectDetail /></UserGuard>},
	{ path: "/projectdetail", element: <UserGuard><ProjectDetail /></UserGuard>},
	{ path: "/schedule-meeting", element: <UserGuard><ScheduleMeeting /></UserGuard>},
	{ path: "/schedulemeeting", element: <UserGuard><ScheduleMeeting /></UserGuard>},

];
