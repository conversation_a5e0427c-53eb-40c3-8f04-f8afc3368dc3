from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from pydantic import BaseModel
from typing import Optional
import databutton as db
from openai import OpenAI
import os

router = APIRouter(prefix="/meetings", tags=["Meetings"])

# Initialize OpenAI client
try:
    client = OpenAI(api_key=db.secrets.get("OPENAI_API_KEY"))
except Exception as e:
    # This will help in debugging if the API key is not set
    print(f"Error initializing OpenAI client: {e}")
    client = None

# --- Pydantic Models ---

class MeetingCreationResponse(BaseModel):
    message: str
    meetingId: str
    title: str
    projectId: str
    transcript: Optional[str] = None

# --- API Endpoints ---

class ScheduleMeetingRequest(BaseModel):
    title: str
    projectId: str
    date: str
    time: str
    attendees: Optional[str] = None
    notes: Optional[str] = None

@router.post("/schedule", response_model=MeetingCreationResponse)
async def schedule_meeting(body: ScheduleMeetingRequest):
    """
    Schedules a new meeting for the future.
    Does not require a transcript or audio file.
    """
    # --- Database Logic (Placeholder) ---
    new_meeting_id = f"m_sched_{os.urandom(4).hex()}"
    print(f"DATABASE_SAVE (Simulated): Scheduled Meeting '{body.title}' for project '{body.projectId}' with ID '{new_meeting_id}'.")
    # ------------------------------------

    return {
        "message": "Meeting scheduled successfully",
        "meetingId": new_meeting_id,
        "title": body.title,
        "projectId": body.projectId,
        "transcript": body.notes, # Using transcript field for notes/agenda
    }


@router.post("", response_model=MeetingCreationResponse)
async def create_meeting_and_process_content(
    title: str = Form(...),
    projectId: str = Form(...),
    date: str = Form(...),
    attendees: Optional[str] = Form(None),
    transcript: Optional[str] = Form(None),
    audio_file: Optional[UploadFile] = File(None),
):
    """
    Creates a new meeting and handles content processing.
    - If a transcript is provided, it's used directly.
    - If an audio file is provided, it is transcribed using Whisper.
    - Either an audio file or a transcript must be provided.
    """
    final_transcript = transcript

    if not audio_file and not transcript:
        raise HTTPException(status_code=400, detail="Either an audio file or a transcript must be provided.")

    if audio_file:
        if not client:
            raise HTTPException(status_code=500, detail="OpenAI client is not initialized. Check API key.")
        
        # Ensure the /tmp directory exists
        if not os.path.exists("/tmp"):
            os.makedirs("/tmp")
            
        file_path = f"/tmp/{audio_file.filename}"
        
        try:
            # Save the uploaded file temporarily
            with open(file_path, "wb") as buffer:
                buffer.write(await audio_file.read())
            
            # Transcribe the audio file
            with open(file_path, "rb") as audio:
                transcription = client.audio.transcriptions.create(
                    model="whisper-1", 
                    file=audio
                )
                final_transcript = transcription.text
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to transcribe audio: {str(e)}")
        finally:
            # Clean up the temporary file
            if os.path.exists(file_path):
                os.remove(file_path)

    # --- Database Logic (Placeholder) ---
    # In a real application, you would save the meeting details 
    # and the final_transcript to your PostgreSQL database here.
    new_meeting_id = f"m_new_{os.urandom(4).hex()}"
    print(f"DATABASE_SAVE (Simulated): Meeting '{title}' for project '{projectId}' with ID '{new_meeting_id}'.")
    # ------------------------------------

    return {
        "message": "Meeting created successfully",
        "meetingId": new_meeting_id,
        "title": title,
        "projectId": projectId,
        "transcript": final_transcript,
    }
