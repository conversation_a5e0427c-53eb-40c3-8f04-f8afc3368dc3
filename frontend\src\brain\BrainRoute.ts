import {
  BodyCreateMeetingAndProcessContent,
  BodyProcessTranscript,
  CheckHealthData,
  CreateMeetingAndProcessContentData,
  GetProjectMeetingsData,
  ProcessTranscriptData,
  ScheduleMeetingData,
  ScheduleMeetingRequest,
} from "./data-contracts";

export namespace Brain {
  /**
   * @description Check health of application. Returns 200 when OK, 500 when not.
   * @name check_health
   * @summary Check Health
   * @request GET:/_healthz
   */
  export namespace check_health {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = CheckHealthData;
  }

  /**
   * No description
   * @tags AI, dbtn/module:ai, dbtn/hasAuth
   * @name process_transcript
   * @summary Process Transcript
   * @request POST:/routes/api/ai/process-transcript
   */
  export namespace process_transcript {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = BodyProcessTranscript;
    export type RequestHeaders = {};
    export type ResponseBody = ProcessTranscriptData;
  }

  /**
   * @description Schedules a new meeting for the future. Does not require a transcript or audio file.
   * @tags Meetings, dbtn/module:meetings, dbtn/hasAuth
   * @name schedule_meeting
   * @summary Schedule Meeting
   * @request POST:/routes/meetings/schedule
   */
  export namespace schedule_meeting {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = ScheduleMeetingRequest;
    export type RequestHeaders = {};
    export type ResponseBody = ScheduleMeetingData;
  }

  /**
   * @description Creates a new meeting and handles content processing. - If a transcript is provided, it's used directly. - If an audio file is provided, it is transcribed using Whisper. - Either an audio file or a transcript must be provided.
   * @tags Meetings, dbtn/module:meetings, dbtn/hasAuth
   * @name create_meeting_and_process_content
   * @summary Create Meeting And Process Content
   * @request POST:/routes/meetings
   */
  export namespace create_meeting_and_process_content {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = BodyCreateMeetingAndProcessContent;
    export type RequestHeaders = {};
    export type ResponseBody = CreateMeetingAndProcessContentData;
  }

  /**
   * No description
   * @tags dbtn/module:projects, dbtn/hasAuth
   * @name get_project_meetings
   * @summary Get Project Meetings
   * @request GET:/routes/project-meetings/{project_id}
   */
  export namespace get_project_meetings {
    export type RequestParams = {
      /** Project Id */
      projectId: string;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = GetProjectMeetingsData;
  }
}
