from fastapi import APIRouter, UploadFile, File, Depends, HTTPException
from pydantic import BaseModel
from typing import List
from openai import OpenAI
import databutton as db
import json

# Initialize OpenAI client
client = None
try:
    client = OpenAI(api_key=db.secrets.get("OPENAI_API_KEY"))
except Exception as e:
    print(f"Warning: OpenAI API key not found. Please add it to use AI features. Error: {e}")

router = APIRouter(prefix="/api/ai", tags=["AI"])

class AISummary(BaseModel):
    summary: str
    key_decisions: List[str]
    action_items: List[str]

def get_openai_client():
    if client is None:
        raise HTTPException(status_code=500, detail="OpenAI client not initialized")
    return client

@router.post("/process-transcript", response_model=AISummary)
async def process_transcript(file: UploadFile = File(...), client: OpenAI = Depends(get_openai_client)):
    transcript_content = await file.read()
    transcript_text = transcript_content.decode("utf-8")

    prompt = f"""
    Analyze the following meeting transcript and extract the following information:
    1. A concise summary of the meeting.
    2. A list of key decisions made.
    3. A list of action items, with clear owners if mentioned.

    Format the output as a JSON object with three keys: "summary", "key_decisions", and "action_items".

    Transcript:
    {transcript_text}
    """

    try:
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a highly skilled assistant that analyzes meeting transcripts."},
                {"role": "user", "content": prompt}
            ],
            response_format={ "type": "json_object" },
            temperature=0.2,
        )
        result = json.loads(response.choices[0].message.content)
        return AISummary(**result)
    except Exception as e:
        print(f"Error processing transcript with OpenAI: {e}")
        # In a real app, you'd want more robust error handling here
        raise HTTPException(status_code=500, detail="Error processing transcript with OpenAI.")
