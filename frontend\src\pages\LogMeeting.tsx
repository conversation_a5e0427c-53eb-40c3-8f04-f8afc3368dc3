import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { ArrowLeft, Upload, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { projects } from "utils/placeholders";
import { auth, API_URL } from "app";
import brain from "brain";

const LogMeeting = () => {
  const navigate = useNavigate();
  const [title, setTitle] = useState("");
  const [projectId, setProjectId] = useState("");
  const [date, setDate] = useState("");
  const [attendees, setAttendees] = useState("");
  const [uploadType, setUploadType] = useState("audio");
  const [transcript, setTranscript] = useState("");
  const [audioFile, setAudioFile] = useState<File | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title || !projectId || !date) {
      toast.error("Please fill in all required fields: Title, Project, and Date.");
      return;
    }
    
    if (uploadType === 'audio' && !audioFile) {
        toast.error("Please select an audio file to upload.");
        return;
    }
    
    if (uploadType === 'transcript' && !transcript) {
        toast.error("Please paste the transcript text.");
        return;
    }

    const toastId = toast.loading("Saving and processing meeting...");

    try {
      const token = await auth.getAuthToken();
      if (!token) {
        toast.error("Authentication failed. Please log in again.", { id: toastId });
        return;
      }

      const formData = new FormData();
      formData.append("title", title);
      formData.append("projectId", projectId);
      formData.append("date", date);
      if (attendees) {
        formData.append("attendees", attendees);
      }
      
      if (uploadType === 'transcript') {
        formData.append("transcript", transcript);
      } else if (audioFile) {
        formData.append("audio_file", audioFile);
      }

      const url = `${API_URL}/meetings`;

      const response = await fetch(url, {
        method: "POST",
        credentials: "include", // Required for auth in dev environment
        headers: {
          Authorization: `Bearer ${token}`,
          // NOTE: Do NOT set 'Content-Type' here. The browser will automatically
          // set it to 'multipart/form-data' with the correct boundary.
        },
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(`Meeting '${result.title}' saved successfully!`, { id: toastId });
        navigate(`/project-detail?id=${result.projectId}`);
      } else {
        // Log detailed error information from the response
        const errorText = await response.text();
        console.error("API Error Response:", errorText);
        console.error("API Error Status:", response.status);
        throw new Error(`Server responded with ${response.status}. Check the console for more details.`);
      }

    } catch (error) {
      console.error("Failed to save meeting:", error);
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred.";
      toast.error(`Error: ${errorMessage}`, { id: toastId });
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-4 sm:p-6 lg:p-8">
      <header className="mb-8">
        <Button variant="ghost" onClick={() => navigate(-1)} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <h1 className="text-4xl font-bold text-gray-800">Log New Meeting</h1>
        <p className="text-lg text-gray-500 mt-1">
          Log a new meeting by uploading an audio file or pasting a transcript.
        </p>
      </header>

      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="glassmorphic-card p-8 rounded-2xl">
          <h2 className="text-2xl font-semibold mb-6">Meeting Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="title">Meeting Title</Label>
              <Input
                id="title"
                placeholder="e.g., Q3 Strategy Review"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="project">Project</Label>
              <Select onValueChange={setProjectId} required>
                <SelectTrigger id="project">
                  <SelectValue placeholder="Select a project" />
                </SelectTrigger>
                <SelectContent>
                  {projects.map((p) => (
                    <SelectItem key={p.id} value={p.id}>
                      {p.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="date">Date</Label>
              <Input
                id="date"
                type="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="attendees">Attendees (Optional)</Label>
              <Input
                id="attendees"
                placeholder="e.g., John, Sarah, Mike"
                value={attendees}
                onChange={(e) => setAttendees(e.target.value)}
              />
            </div>
          </div>
        </div>

        <div className="glassmorphic-card p-8 rounded-2xl">
           <h2 className="text-2xl font-semibold mb-6">Content</h2>
          <Tabs defaultValue="audio" className="w-full" onValueChange={setUploadType}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="audio">
                <Upload className="mr-2 h-4 w-4"/>
                Upload Audio
              </TabsTrigger>
              <TabsTrigger value="transcript">
                <FileText className="mr-2 h-4 w-4"/>
                Paste Transcript
              </TabsTrigger>
            </TabsList>
            <TabsContent value="audio" className="pt-6">
                <Label htmlFor="audio-file" className="sr-only">Upload Audio File</Label>
                <Input 
                    id="audio-file" 
                    type="file" 
                    accept="audio/*"
                    onChange={(e) => setAudioFile(e.target.files ? e.target.files[0] : null)}
                    className="cursor-pointer"
                />
                 <p className="text-sm text-gray-500 mt-2">Supported formats: .mp3, .wav, .m4a, etc.</p>
            </TabsContent>
            <TabsContent value="transcript" className="pt-6">
               <Label htmlFor="transcript-text" className="sr-only">Paste Transcript</Label>
              <Textarea
                id="transcript-text"
                placeholder="Paste your full meeting transcript here..."
                rows={12}
                value={transcript}
                onChange={(e) => setTranscript(e.target.value)}
              />
            </TabsContent>
          </Tabs>
        </div>
        
        <div className="flex justify-end pt-4">
            <Button type="submit" size="lg" className="bg-blue-600 text-white hover:bg-blue-700">
                Save Meeting
            </Button>
        </div>
      </form>
    </div>
  );
};

export default LogMeeting;
