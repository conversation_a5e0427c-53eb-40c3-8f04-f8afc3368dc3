{"name": "cloneable-readable", "version": "1.1.3", "description": "Clone a Readable stream, safely", "main": "index.js", "scripts": {"test": "standard && tape test.js | tap-spec"}, "precommit": "test", "repository": {"type": "git", "url": "git+https://github.com/mcollina/cloneable-readable.git"}, "keywords": ["readable", "stream", "clone"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/mcollina/cloneable-readable/issues"}, "homepage": "https://github.com/mcollina/cloneable-readable#readme", "devDependencies": {"flush-write-stream": "^1.0.0", "from2": "^2.1.1", "pre-commit": "^1.1.2", "pump": "^3.0.0", "standard": "^11.0.0", "tap-spec": "^4.1.1", "tape": "^4.9.0"}, "dependencies": {"inherits": "^2.0.1", "process-nextick-args": "^2.0.0", "readable-stream": "^2.3.5"}}