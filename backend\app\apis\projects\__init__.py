from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import databutton as db
import psycopg2
import psycopg2.extras

router = APIRouter()

class Meeting(BaseModel):
    id: str
    title: str
    project_id: str
    date: str
    status: str

@router.get("/project-meetings/{project_id}", response_model=list[Meeting])
async def get_project_meetings(project_id: str):
    try:
        conn = psycopg2.connect(db.secrets.get("DATABASE_URL_DEV"))
        cur = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        cur.execute("SELECT id, title, project_id, date, status FROM meetings WHERE project_id = %s", (project_id,))
        meetings = cur.fetchall()
        cur.close()
        conn.close()
        return [Meeting(id=row['id'], title=row['title'], project_id=row['project_id'], date=row['date'], status=row['status']) for row in meetings]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
