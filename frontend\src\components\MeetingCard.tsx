import React from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

import {
  Calendar,
  Clock,
  Users,
  CheckCircle,
  DollarSign,
  FileText,
  Send,
} from "lucide-react";

interface Meeting {
    id: string;
    title: string;
    clientName: string;
    description: string;
    date: string;
    duration: number;
    attendees: string[];
    actionItemsCount: number;
    cost: number;
    tags: string[];
}

interface Props {
    meeting: Meeting;
}

export const MeetingCard = ({ meeting }: Props) => {
    return (
        <div className="bg-white/60 backdrop-blur-lg border border-white/30 rounded-2xl p-6 shadow-sm">
            <div className="flex justify-between items-start mb-2">
                <div>
                    <h3 className="text-lg font-bold text-gray-900">{meeting.title}</h3>
                    <p className="text-sm text-gray-600">{meeting.clientName}</p>
                </div>
                <div className="flex items-center space-x-2">
                    {meeting.tags.map(tag => (
                         <Badge key={tag} variant="outline" className="capitalize border-green-200 bg-green-50 text-green-700">{tag}</Badge>
                    ))}
                </div>
            </div>
            
            <p className="text-sm text-gray-500 mb-4">{meeting.description}</p>

            <div className="flex flex-wrap items-center text-sm text-gray-600 mb-4 gap-x-4 gap-y-2">
                <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1.5"/>
                    {meeting.date}
                </div>
                <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-1.5"/>
                    {meeting.duration} min
                </div>
                <div className="flex items-center">
                    <Users className="h-4 w-4 mr-1.5"/>
                    Attendees: {meeting.attendees.join(", ")}
                </div>
            </div>

            <div className="border-t border-gray-200/80 my-4" />

            <div className="flex justify-between items-center">
                <div className="flex items-center space-x-6 text-sm">
                     <div className="flex items-center text-gray-600">
                        <CheckCircle className="h-4 w-4 mr-1.5 text-gray-500"/>
                        {meeting.actionItemsCount} action items
                    </div>
                     <div className="flex items-center text-gray-600">
                        <DollarSign className="h-4 w-4 mr-1.5 text-gray-500"/>
                        ${meeting.cost.toFixed(2)}
                    </div>
                </div>
                <div className="flex items-center space-x-2">
                    <Button variant="link" className="text-indigo-600">View Details</Button>
                    <Button variant="link" className="text-indigo-600"><FileText className="h-4 w-4 mr-1"/>View Transcript</Button>
                    <Button variant="link" className="text-indigo-600"><Send className="h-4 w-4 mr-1"/>Send Summary</Button>
                </div>
            </div>
        </div>
    )
}
