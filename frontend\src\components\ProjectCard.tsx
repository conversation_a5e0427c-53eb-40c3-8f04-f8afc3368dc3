import { useNavigate } from "react-router-dom";
import { Folder, Users, Calendar } from "lucide-react";

export interface Project {
  id: string;
  name: string;
  client: string;
  meetingCount: number;
  teamMembers: number;
}

interface Props {
  project: Project;
}

export const ProjectCard = ({ project }: Props) => {
  const navigate = useNavigate();

  const handleProjectClick = () => {
    navigate(`/project-detail?id=${project.id}`);
  };

  return (
    <div 
      className="glassmorphic-card p-6 rounded-2xl flex flex-col justify-between hover:border-blue-400 transition-colors group cursor-pointer"
      onClick={handleProjectClick}
    >
      <div>
        <div className="flex items-center justify-between mb-4">
           <Folder className="h-8 w-8 text-blue-500" />
           <span className="text-xs font-semibold bg-blue-100 text-blue-700 px-2 py-1 rounded-full">{project.client}</span>
        </div>
        <h3 className="text-xl font-bold mb-2">{project.name}</h3>
      </div>
      <div className="mt-4 flex items-center justify-between text-sm text-gray-600 border-t border-white/20 pt-4">
        <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <span>{project.meetingCount} Meetings</span>
        </div>
        <div className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span>{project.teamMembers} Members</span>
        </div>
      </div>
    </div>
  );
};
