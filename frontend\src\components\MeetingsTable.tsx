import { useNavigate } from "react-router-dom";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { projects, type Meeting } from "utils/placeholders";
import { Badge } from "@/components/ui/badge";
import { MoreH<PERSON>zontal, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";

interface Props {
    meetings: Meeting[];
}


const getStatusBadge = (status: string) => {
    switch (status) {
        case "Transcribed":
            return <Badge variant="outline" className="text-green-600 border-green-600">Transcribed</Badge>;
        case "Summary Sent":
            return <Badge variant="outline" className="text-blue-600 border-blue-600">Summary Sent</Badge>;
        case "Processing":
            return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Processing</Badge>;
        default:
            return <Badge variant="secondary">{status}</Badge>;
    }
};

export const MeetingsTable = ({ meetings }: Props) => {
  const navigate = useNavigate();
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Meeting Title</TableHead>
          <TableHead>Project</TableHead>
          <TableHead>Date</TableHead>
          <TableHead>Status</TableHead>
          <TableHead className="text-right"></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {meetings.map((meeting) => (
          <TableRow key={meeting.id} className="hover:bg-gray-50/50">
            <TableCell className="font-medium">{meeting.title}</TableCell>
            <TableCell>{projects.find(p => p.id === meeting.projectId)?.name}</TableCell>
            <TableCell>{meeting.date}</TableCell>
            <TableCell>{getStatusBadge(meeting.status)}</TableCell>
            <TableCell className="text-right">
                <Button variant="ghost" size="sm" onClick={() => navigate(`/meeting-detail?id=${meeting.id}`)}>
                    View Details <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};
