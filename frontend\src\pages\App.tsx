import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useU<PERSON>, UserButton } from "@stackframe/react";
import {
  PlayCircle,
  Mic,
  CheckSquare,
  Calculator,
  FileText,
  Bell,
  Sparkles,
  Zap,
  ArrowRight,
  Twitter,
  Linkedin,
  Mail,
  Star,
} from "lucide-react";

export default function App() {
  const user = useUser();
  return (
    <div className="bg-gradient-to-br from-blue-50 via-white to-blue-100 min-h-screen text-gray-800 font-sans">
      <div className="absolute top-0 left-0 w-full h-full bg-no-repeat bg-cover opacity-10 mix-blend-multiply" style={{backgroundImage: "url('https://www.transparenttextures.com/patterns/cubes.png')"}}></div>
      <div className="relative">
        <header className="sticky top-0 z-50 w-full bg-white/95 backdrop-blur-sm shadow-sm">
          <div className="container mx-auto flex items-center justify-between px-4 py-4 sm:px-6 lg:px-8">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-600 p-2 rounded-lg">
                <Mic className="h-5 w-5 text-white" />
              </div>
              <span className="text-2xl font-bold">KaiNote</span>
            </div>
            <nav className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-gray-600 hover:text-blue-600">
                Features
              </a>
              <a href="#pricing" className="text-gray-600 hover:text-blue-600">
                Pricing
              </a>
            </nav>
            <div className="flex items-center space-x-4">
              {user ? (
                <UserButton />
              ) : (
                <>
                  <Button asChild variant="ghost">
                    <a href="/auth/sign-in">Sign In</a>
                  </Button>
                  <Button asChild variant="outline" className="rounded-full">
                    <a href="/auth/sign-up">Sign Up</a>
                  </Button>
                  <Button
                    asChild
                    className="bg-blue-600 text-white hover:bg-blue-700 rounded-full"
                  >
                    <a href="#">Add Extension</a>
                  </Button>
                </>
              )}
            </div>
          </div>
        </header>

        <main className="container mx-auto px-4 pt-20 pb-32 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="text-left">
              <div className="mb-4 inline-flex items-center bg-blue-100 text-blue-700 text-sm font-semibold px-4 py-1 rounded-full">
                <Sparkles className="h-4 w-4 mr-2" />
                Designed for freelancers & solopreneurs
              </div>
              <h1 className="text-5xl md:text-7xl font-extrabold tracking-tight mb-4">
                Meetings that <span className="text-blue-600">work for you</span>
              </h1>
              <p className="text-lg text-gray-600 mb-8">
                Transform virtual client meetings into actionable results. Record,
                transcribe, and extract your tasks automatically – so freelancers
                and solopreneurs can focus on what they do best.
              </p>
              <div className="flex items-center space-x-4">
                <Button size="lg" className="bg-blue-600 text-white hover:bg-blue-700 rounded-full px-8 py-6 text-lg">
                  Try Demo <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button size="lg" variant="outline" className="rounded-full px-8 py-6 text-lg">
                  <PlayCircle className="mr-2 h-5 w-5" /> Watch Demo
                </Button>
              </div>
            </div>
            
            <div className="relative animate-float">
              <div className="absolute -inset-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-3xl opacity-20 blur-2xl"></div>
              <div className="relative glassmorphic-card p-6 rounded-2xl shadow-2xl">
                <div className="flex items-center justify-between p-2 bg-gray-100 rounded-t-lg">
                    <div className="flex items-center space-x-2">
                        <span className="h-3 w-3 rounded-full bg-red-500"></span>
                        <span className="h-3 w-3 rounded-full bg-yellow-500"></span>
                        <span className="h-3 w-3 rounded-full bg-green-500"></span>
                    </div>
                    <div className="text-sm text-gray-500 font-medium">Client Meeting - 45 min</div>
                    <div></div>
                </div>
                <div className="p-6">
                    <div className="space-y-3">
                        <div className="h-4 bg-gray-200 rounded-full w-3/4"></div>
                        <div className="h-4 bg-gray-200 rounded-full w-full"></div>
                        <div className="h-4 bg-gray-200 rounded-full w-1/2"></div>
                    </div>
                    <div className="mt-8 space-y-4">
                        <div className="flex items-center">
                            <CheckSquare className="h-6 w-6 text-green-500 mr-3" />
                            <span className="text-lg">Design wireframes by Friday</span>
                        </div>
                        <div className="flex items-center opacity-50">
                            <CheckSquare className="h-6 w-6 text-gray-400 mr-3" />
                            <span className="text-lg">Send pricing proposal</span>
                        </div>
                        <div className="flex items-center opacity-50">
                            <CheckSquare className="h-6 w-6 text-gray-400 mr-3" />
                            <span className="text-lg">Schedule follow-up meeting</span>
                        </div>
                    </div>
                </div>
              </div>
            </div>
          </div>
        </main>

        <section id="features" className="py-20 bg-white/50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold tracking-tight">
                Built for freelancers & solopreneurs who value their time
              </h2>
              <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
                Every feature is designed to save you hours on administrative
                work so you can focus on delivering great work for your clients
                and growing your business.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <FeatureCard
                icon={<Mic className="h-6 w-6" />}
                title="Smart Recording & Transcription"
                description="One-click recording for Zoom, Meet, and Teams. Automatic transcription with speaker identification for freelancers and solopreneurs."
                iconBgColor="bg-blue-600"
              />
              <FeatureCard
                icon={<CheckSquare className="h-6 w-6" />}
                title="Freelancer-Focused Action Items"
                description="AI extracts only YOUR tasks from meetings – no client summaries, just what you need to deliver and get paid."
                iconBgColor="bg-green-500"
              />
              <FeatureCard
                icon={<Calculator className="h-6 w-6" />}
                title="Meeting Cost Calculator"
                description="See the real cost of meetings in time and money. Helps freelancers and solo pros optimize their billable hours."
                iconBgColor="bg-orange-500"
              />
              <FeatureCard
                icon={<Mail className="h-6 w-6" />}
                title="Professional Client Summaries"
                description="Auto-generated meeting summaries to send to clients with deliverables and deadlines – perfect for solopreneurs."
                iconBgColor="bg-purple-500"
              />
              <FeatureCard
                icon={<Bell className="h-6 w-6" />}
                title="Smart Reminders"
                description="Never miss a deadline with intelligent task reminders and weekly digest emails tailored for independent professionals."
                iconBgColor="bg-red-500"
              />
              <FeatureCard
                icon={<FileText className="h-6 w-6" />}
                title="Lightweight Task Management"
                description="Clean to-do list for all your meeting tasks. Check off, reschedule, or export with ease – built for solo workflows."
                iconBgColor="bg-blue-500"
              />
            </div>
          </div>
        </section>

        <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="bg-blue-50 rounded-2xl p-12">
            <div className="grid md:grid-cols-3 gap-8 text-center">
              <div>
                <p className="text-5xl font-bold text-blue-600">75-85%</p>
                <p className="text-lg text-gray-600 mt-2">of meetings are virtual</p>
              </div>
              <div>
                <p className="text-5xl font-bold text-green-500">3-5 hrs</p>
                <p className="text-lg text-gray-600 mt-2">saved per week</p>
              </div>
              <div>
                <p className="text-5xl font-bold text-orange-500">100%</p>
                <p className="text-lg text-gray-600 mt-2">focused on your tasks</p>
              </div>
            </div>
            <p className="text-center text-2xl font-medium text-gray-700 mt-12">
              "Less admin, more action."
            </p>
            <p className="text-center text-lg text-gray-600 mt-4">
              Join thousands of freelancers and solopreneurs who've reclaimed their time with KaiNote.
            </p>
          </div>
        </section>

        <section id="pricing" className="py-20">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold tracking-tight">
                Choose the plan that's right for you
              </h2>
              <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
                Start for free, then upgrade to unlock powerful features that
                save you time and money.
              </p>
            </div>
            <div className="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto items-start">
              <PricingCard
                plan="Free"
                price="0"
                priceDetail="forever"
                description="Perfect for freelancers trying out KaiNote"
                features={[
                  "30 minutes per month",
                  "Basic action item extraction",
                  "Email reminders",
                  "Meeting transcription",
                  "Chrome extension",
                ]}
                buttonText="Start Free"
                variant="secondary"
              />
              <PricingCard
                plan="Pro"
                price="9.99"
                priceDetail="per month"
                description="For productive freelancers & solo professionals"
                features={[
                  "300 minutes per month",
                  "AI-powered agenda suggestions",
                  "Smart task reminders",
                  "Client summary generator",
                  "Meeting cost calculator",
                  "Priority support",
                  "Export to popular tools",
                ]}
                buttonText="Start Pro Trial"
                variant="primary"
                popular={true}
              />
              <PricingCard
                plan="Business"
                price="19.98"
                priceDetail="per month"
                description="For power users and small teams"
                features={[
                  "900 minutes per month",
                  "All Pro features",
                  "Team Workspaces",
                  "Advanced analytics",
                  "Custom branding",
                ]}
                buttonText="Get Started"
                variant="secondary"
              />
            </div>
          </div>
        </section>

        <section className="py-20 bg-white/50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
             <div className="text-center mb-16">
              <h2 className="text-4xl font-bold tracking-tight">
                Loved by freelancers worldwide
              </h2>
               <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
                Don't just take our word for it. Here's what solopreneurs are saying about KaiNote.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              <ReviewCard
                text="KaiNote has been a game-changer. I'm saving hours every week on meeting admin and can focus purely on client work."
                author="Sarah K."
                role="Web Designer"
                avatarUrl="https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80"
              />
              <ReviewCard
                text="The automatic action items are incredibly accurate. It's like having a personal assistant for every meeting."
                author="David L."
                role="Marketing Consultant"
                avatarUrl="https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80"
              />
              <ReviewCard
                text="As a solopreneur, every minute counts. KaiNote helps me make the most of my time and look incredibly professional to my clients."
                author="Maria G."
                role="Copywriter"
                avatarUrl="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80"
              />
            </div>
          </div>
        </section>

        <footer className="bg-[#111827] text-gray-300">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="lg:col-span-1">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="bg-blue-600 p-2 rounded-lg">
                    <Mic className="h-6 w-6 text-white" />
                  </div>
                  <span className="text-2xl font-bold text-white">KaiNote</span>
                </div>
                <p className="max-w-md text-gray-400 text-sm">
                  Transform virtual client meetings into actionable results.
                </p>
                <div className="flex space-x-4 mt-6">
                  <a href="#" className="text-gray-400 hover:text-white">
                    <Twitter className="h-5 w-5" />
                  </a>
                  <a href="#" className="text-gray-400 hover:text-white">
                    <Linkedin className="h-5 w-5" />
                  </a>
                  <a href="#" className="text-gray-400 hover:text-white">
                    <Mail className="h-5 w-5" />
                  </a>
                </div>
              </div>
              <div className="lg:col-start-3">
                <h3 className="text-white font-semibold mb-4">Product</h3>
                <ul className="space-y-3">
                  <li>
                    <a href="#features" className="text-gray-400 hover:text-white text-sm">
                      Features
                    </a>
                  </li>
                  <li>
                    <a href="#pricing" className="text-gray-400 hover:text-white text-sm">
                      Pricing
                    </a>
                  </li>
                  <li>
                    <a href="#" className="text-gray-400 hover:text-white text-sm">
                      Chrome Extension
                    </a>
                  </li>
                  <li>
                    <a href="#" className="text-gray-400 hover:text-white text-sm">
                      API
                    </a>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-white font-semibold mb-4">Support</h3>
                <ul className="space-y-3">
                  <li>
                    <a href="#" className="text-gray-400 hover:text-white text-sm">
                      Help Center
                    </a>
                  </li>
                  <li>
                    <a href="#" className="text-gray-400 hover:text-white text-sm">
                      Contact Us
                    </a>
                  </li>
                  <li>
                    <a href="#" className="text-gray-400 hover:text-white text-sm">
                      Privacy Policy
                    </a>
                  </li>
                  <li>
                    <a href="#" className="text-gray-400 hover:text-white text-sm">
                      Terms of Service
                    </a>
                  </li>
                </ul>
              </div>
            </div>
            <div className="mt-16 border-t border-gray-700 pt-8 flex justify-between items-center">
              <p className="text-gray-500 text-sm">
                © {new Date().getFullYear()} KaiNote. All rights reserved.
              </p>
               <p className="text-gray-500 text-sm">
                Made for freelancers, by freelancers.
              </p>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
}

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  iconBgColor: string;
}

const FeatureCard = ({ icon, title, description, iconBgColor }: FeatureCardProps) => (
  <div className="bg-white p-8 rounded-2xl border border-gray-200 text-left shadow-sm hover:shadow-lg transition-shadow">
    <div className={`${iconBgColor} text-white rounded-lg p-3 mb-6 inline-block`}>
      {icon}
    </div>
    <h3 className="text-xl font-semibold mb-2">{title}</h3>
    <p className="text-gray-600">{description}</p>
  </div>
);

interface PricingCardProps {
  plan: string;
  price: string;
  priceDetail: string;
  description: string;
  features: string[];
  buttonText: string;
  variant: "primary" | "secondary";
  popular?: boolean;
}

const PricingCard = ({
  plan,
  price,
  priceDetail,
  description,
  features,
  buttonText,
  variant,
  popular,
}: PricingCardProps) => (
  <div
    className={`relative p-8 rounded-2xl border ${
      popular
        ? "bg-white shadow-2xl border-blue-500"
        : "bg-white border-gray-200"
    }`}
  >
    {popular && (
      <div className="absolute -top-4 left-1/2 -translate-x-1/2 bg-blue-600 text-white text-sm font-semibold px-4 py-1 rounded-full flex items-center space-x-1">
        <Star className="h-4 w-4" />
        <span>Most Popular</span>
      </div>
    )}
    <div className="text-center">
      <h3 className="text-2xl font-semibold">{plan}</h3>
      <p className="mt-2 text-gray-600">{description}</p>
      <p className="mt-6 text-5xl font-bold">
        ${price}
        <span className="text-lg font-medium text-gray-500 ml-1">
          {priceDetail}
        </span>
      </p>
    </div>
    <ul className="mt-8 mb-8 space-y-4 text-left">
      {features.map((feature, i) => (
        <li key={i} className="flex items-center">
          <CheckSquare className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
          <span className="text-gray-700">{feature}</span>
        </li>
      ))}
    </ul>
    <Button
      size="lg"
      className={`w-full mt-auto text-lg py-6 ${
        variant === "primary"
          ? "bg-blue-600 text-white hover:bg-blue-700"
          : "bg-gray-900 text-white hover:bg-gray-800"
      }`}
    >
      {buttonText}
    </Button>
  </div>
);

interface ReviewCardProps {
    text: string;
    author: string;
    role: string;
    avatarUrl: string;
}

const ReviewCard = ({ text, author, role, avatarUrl }: ReviewCardProps) => (
    <div className="glassmorphic-card p-8 rounded-2xl flex flex-col">
        <p className="text-gray-700 mb-6 flex-grow">"{text}"</p>
        <div className="flex items-center">
            <img src={avatarUrl} alt={author} className="w-12 h-12 rounded-full mr-4 object-cover" />
            <div>
                <p className="font-semibold">{author}</p>
                <p className="text-sm text-gray-500">{role}</p>
            </div>
        </div>
    </div>
)
