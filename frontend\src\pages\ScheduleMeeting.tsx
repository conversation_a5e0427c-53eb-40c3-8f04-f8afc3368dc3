import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import brain from "brain";

const ScheduleMeeting = () => {
  const navigate = useNavigate();
  const [title, setTitle] = useState("");
  const [project, setProject] = useState("");
  const [date, setDate] = useState("");
  const [time, setTime] = useState("");
  const [attendees, setAttendees] = useState("");
  const [notes, setNotes] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const payload = {
        title,
        projectId: project, // Assuming 'project' state holds the ID
        date,
        time,
        attendees,
        notes,
      };
      
      const response = await brain.schedule_meeting(payload);
      
      if (response.ok) {
        navigate("/dashboard");
      } else {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to schedule meeting");
      }
      
    } catch (error) {
      console.error("Failed to schedule meeting:", error);
      // You can use toast to show the error message
      // import { toast } from "sonner";
      // toast.error(error.message);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Schedule New Meeting</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <label htmlFor="title">Meeting Title</label>
              <Input id="title" placeholder="e.g., Project Kickoff" value={title} onChange={(e) => setTitle(e.target.value)} required />
            </div>
            <div className="space-y-2">
              <label htmlFor="project">Project</label>
              <Input id="project" placeholder="e.g., Website Redesign" value={project} onChange={(e) => setProject(e.target.value)} required />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="date">Date</label>
                <Input id="date" type="date" value={date} onChange={(e) => setDate(e.target.value)} required />
              </div>
              <div className="space-y-2">
                <label htmlFor="time">Time</label>
                <Input id="time" type="time" value={time} onChange={(e) => setTime(e.target.value)} required />
              </div>
            </div>
             <div className="space-y-2">
                <label htmlFor="attendees">Attendees (optional)</label>
                <Input id="attendees" type="number" placeholder="e.g., 3" value={attendees} onChange={(e) => setAttendees(e.target.value)} />
              </div>
            <div className="space-y-2">
                <label htmlFor="notes">Agenda / Notes (optional)</label>
                <Textarea id="notes" placeholder="e.g., - Discuss project goals..." value={notes} onChange={(e) => setNotes(e.target.value)} />
            </div>
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="ghost" onClick={() => navigate(-1)}>Cancel</Button>
              <Button type="submit">Schedule Meeting</Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default ScheduleMeeting;
