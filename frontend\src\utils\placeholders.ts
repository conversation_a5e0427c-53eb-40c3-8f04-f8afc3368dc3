export const projects: Project[] = [
  {
    id: "p1",
    name: "Website Redesign",
    client: "TechStart Inc.",
    meetingCount: 5,
    teamMembers: 4,
  },
  {
    id: "p2",
    name: "Mobile App Development",
    client: "Innovate LLC",
    meetingCount: 12,
    teamMembers: 6,
  },
  {
    id: "p3",
    name: "Q3 Marketing Campaign",
    client: "MarketBoost",
    meetingCount: 8,
    teamMembers: 3,
  },
  {
    id: "p4",
    name: "Brand Identity Refresh",
    client: "Creative Solutions",
    meetingCount: 3,
    teamMembers: 2,
  },
];

export interface Meeting {
  id: string;
  projectId: string;
  title: string;
  date: string;
  time: string;
  duration: string;
  attendees: number;
  cost: number;
  overview: string;
  keyDecisions: string[];
  transcript: { speaker: string; line: string }[];
  actionItems: ActionItem[];
  clientSummary: string;
  attachments: { name: string; type: "file" | "link"; url: string }[];
  followUpNotes: string;
}

export interface ActionItem {
  id: string;
  task: string;
  dueDate: string;
  priority: "high" | "urgent" | "medium" | "low";
}

