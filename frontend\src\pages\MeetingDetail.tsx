import React from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { meetings, projects } from "utils/placeholders";
import {
  ArrowLeft,
  Download,
  Share2,
  Send,
  Calendar,
  Clock,
  Users,
  DollarSign,
  Copy,
  Mail,
  Paperclip,
  Plus,
  Share,
} from "lucide-react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

const MeetingDetail = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const meetingId = searchParams.get("id");
  const meeting = meetings.find((m) => m.id === meetingId);
  const project = projects.find((p) => p.id === meeting?.projectId);

  if (!meeting || !project) {
    return (
      <div className="flex items-center justify-center h-screen">
        <p className="text-2xl">Meeting not found.</p>
      </div>
    );
  }

  const handleDownloadTranscript = () => {
    const transcriptText = (meeting.transcript || [])
      .map((t) => `[${t.speaker}]: ${t.line}`)
      .join("\\n");
    const blob = new Blob([transcriptText], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `transcript-${meeting.title.replace(/\\s+/g, "_")}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleActionClick = (action: string) => {
    toast.info("Coming Soon!", {
      description: `The "${action}" functionality is under development.`,
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-100 p-4 sm:p-6 lg:p-8">
      <div className="max-w-6xl mx-auto">
        <header className="mb-8">
          <Button variant="ghost" onClick={() => navigate(-1)} className="mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Project
          </Button>
          <div className="flex flex-wrap justify-between items-start gap-4">
            <div>
              <h1 className="text-4xl font-bold">{meeting.title}</h1>
              <p className="text-lg text-gray-500 mt-1">
                For{" "}
                <span className="font-semibold text-gray-700">
                  {project.client}
                </span>
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="bg-white/50"
                onClick={() => handleActionClick("Share")}
              >
                <Share2 className="mr-2 h-4 w-4" /> Share
              </Button>
              <Button
                variant="outline"
                className="bg-white/50"
                onClick={() => handleActionClick("Send Summary")}
              >
                <Send className="mr-2 h-4 w-4" /> Send Summary
              </Button>
              <Button
                className="bg-blue-600 text-white hover:bg-blue-700"
                onClick={handleDownloadTranscript}
              >
                <Download className="mr-2 h-4 w-4" /> Download Transcript
              </Button>
            </div>
          </div>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <Tabs defaultValue="summary" className="w-full">
              <TabsList className="grid w-full grid-cols-5 mb-6">
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="transcript">Transcript</TabsTrigger>
                <TabsTrigger value="action-items">Action Items</TabsTrigger>
                <TabsTrigger value="attachments">Attachments</TabsTrigger>
                <TabsTrigger value="follow-up">Follow-up</TabsTrigger>
              </TabsList>

              <TabsContent
                value="summary"
                className="glassmorphic-card p-6 sm:p-8 rounded-2xl"
              >
                <h2 className="text-2xl font-bold mb-4">Summary</h2>
                <div className="space-y-6">
                  <div>
                    <h3 className="font-semibold text-lg mb-2">Key Points</h3>
                    <ul className="list-disc list-inside space-y-2 text-gray-700">
                      {(meeting.keyDecisions || []).map((decision, index) => (
                        <li key={index}>{decision}</li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-2">
                      Decisions Made
                    </h3>
                    <ul className="list-disc list-inside space-y-2 text-gray-700">
                      {(meeting.keyDecisions || []).map((decision, index) => (
                        <li key={index}>{decision}</li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-2">
                      AI Generated Summary
                    </h3>
                    <p className="text-gray-700 whitespace-pre-line">
                      {meeting.clientSummary}
                    </p>
                  </div>
                </div>
              </TabsContent>

              <TabsContent
                value="transcript"
                className="glassmorphic-card p-6 sm:p-8 rounded-2xl"
              >
                <h2 className="text-2xl font-bold mb-6">Full Transcript</h2>
                <div className="space-y-4">
                  {(meeting.transcript || []).map((item, index) => (
                    <div
                      key={index}
                      className="grid grid-cols-[auto,1fr] gap-x-4"
                    >
                      <span className="font-semibold text-gray-600">
                        {item.speaker}:
                      </span>
                      <p className="text-gray-800">{item.line}</p>
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent
                value="action-items"
                className="glassmorphic-card p-6 sm:p-8 rounded-2xl"
              >
                <h2 className="text-2xl font-bold mb-6">Action Items</h2>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Task</TableHead>
                      <TableHead>Assigned To</TableHead>
                      <TableHead>Due Date</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {(meeting.actionItems || []).map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{item.task}</TableCell>
                        <TableCell>You</TableCell>
                        <TableCell>{item.dueDate}</TableCell>
                        <TableCell>
                          <Badge variant="outline">In Progress</Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>

              <TabsContent
                value="attachments"
                className="glassmorphic-card p-6 sm:p-8 rounded-2xl"
              >
                <h2 className="text-2xl font-bold mb-6">Attachments</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {(meeting.attachments || []).map((attachment, index) => (
                    <div
                      key={index}
                      className="border rounded-lg p-4 flex items-center space-x-4"
                    >
                      <Paperclip className="h-6 w-6 text-gray-500" />
                      <div>
                        <p className="font-semibold">{attachment.name}</p>
                        <a href={attachment.url} className="text-sm text-blue-600">
                          {attachment.type === "file" ? "Download" : "Open"}
                        </a>
                      </div>
                    </div>
                  ))}
                </div>
                <Button variant="outline" className="mt-6">
                  <Plus className="mr-2 h-4 w-4" /> Add Attachment
                </Button>
              </TabsContent>

              <TabsContent
                value="follow-up"
                className="glassmorphic-card p-6 sm:p-8 rounded-2xl"
              >
                <h2 className="text-2xl font-bold mb-6">Follow-up</h2>
                <div className="space-y-4">
                  <p className="text-gray-600">Notes for next meeting:</p>
                  <textarea
                    className="w-full p-2 border rounded-lg"
                    rows={4}
                    placeholder="e.g., Discuss budget for Q4..."
                    defaultValue={meeting.followUpNotes}
                  ></textarea>
                  <Button className="bg-blue-600 text-white">
                    Schedule Next Meeting
                  </Button>
                </div>
                <div className="mt-8">
                  <h3 className="font-semibold text-lg mb-2">
                    Smart Suggestions
                  </h3>
                  <p className="text-gray-500">
                    You haven't sent the pricing proposal yet.
                  </p>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          <aside className="lg:col-span-1 space-y-6">
            <div className="glassmorphic-card p-6 rounded-2xl">
              <h2 className="text-xl font-semibold mb-4">General Info</h2>
              <div className="space-y-4 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500 flex items-center">
                    <Calendar className="mr-2 h-4 w-4" /> Date & Time
                  </span>
                  <span>
                    {meeting.date} at {meeting.time}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500 flex items-center">
                    <Clock className="mr-2 h-4 w-4" /> Duration
                  </span>
                  <span>{meeting.duration}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500 flex items-center">
                    <Users className="mr-2 h-4 w-4" /> Attendees
                  </span>
                  <span>{meeting.attendees}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500 flex items-center">
                    <DollarSign className="mr-2 h-4 w-4" /> Cost
                  </span>
                  <span>${meeting.cost.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500 flex items-center">
                    <Share className="mr-2 h-4 w-4" /> Calendar Sync
                  </span>
                  <Badge variant="secondary" className="bg-green-100 text-green-700">
                    Synced
                  </Badge>
                </div>
              </div>
            </div>
            <div className="glassmorphic-card p-6 rounded-2xl">
              <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <Copy className="mr-2 h-4 w-4" /> Copy Summary
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Mail className="mr-2 h-4 w-4" /> Email to Client
                </Button>
              </div>
            </div>
          </aside>
        </div>
      </div>
    </div>
  );
};

export default MeetingDetail;
